import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import axios from 'axios'

// 创建 Axios 实例
function createAxiosInstance(baseURL: string): AxiosInstance {
  const instance = axios.create({
    baseURL,
    timeout: 10000, // 请求超时时间
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 在发送请求之前做些什么，例如添加 token
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      // 对请求错误做些什么
      return Promise.reject(error)
    },
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // 对响应数据做些什么
      return response.data
    },
    (error) => {
      // 对响应错误做些什么
      if (error.response) {
        switch (error.response.status) {
          case 401:
            // 处理未授权的情况
            break
          case 404:
            // 处理未找到资源的情况
            break
          default:
            // 处理其他错误
            break
        }
      }
      return Promise.reject(error)
    },
  )

  return instance
}

export default createAxiosInstance

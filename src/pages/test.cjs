// 引入所需的库
// @polkadot/types 是核心，用于类型定义和注册
// axios 用于发送底层的 HTTP RPC 请求
const { TypeRegistry, Metadata } = require('@polkadot/types');
const { xxhashAsHex } = require('@polkadot/util-crypto');
const axios = require('axios'); // 你也可以用 node-fetch 或其他 HTTP 客户端

// --- 配置 ---
const HTTP_RPC_URL = 'https://rpc.polkadot.io';
const anAddress = '************************************************'; // 一个有效的 Polkadot 地址

// 用于存储从链上获取的元数据
let savedMetadataHex;


// ===================================================================
// 第 1 步: 手动获取元数据 (可以独立运行，一次即可)
// ===================================================================
async function fetchAndSaveMetadata() {
  console.log('🚀 开始手动获取元数据...');

  try {
    // 构造底层的 JSON-RPC 请求体
    const requestPayload = {
      id: 1,
      jsonrpc: '2.0',
      method: 'state_getMetadata',
      params: []
    };

    // 使用 axios 发送原始的 POST 请求
    const response = await axios.post(HTTP_RPC_URL, requestPayload);

    if (response.data.error) {
      throw new Error(`RPC 错误: ${response.data.error.message}`);
    }

    // 从响应中获取元数据16进制字符串并保存
    savedMetadataHex = response.data.result;
    console.log('✅ 成功获取并保存元数据!',savedMetadataHex);
    // console.log('元数据 (部分):', savedMetadataHex.substring(0, 200), '...');

  } catch (error) {
    console.error('❌ 获取元数据失败:', error.message);
  }
}


// ===================================================================
// 第 2 步: 使用已保存的元数据进行编解码 (业务逻辑)
// ===================================================================
function useSavedMetadata() {
  console.log('\n🔥 开始使用已保存的元数据进行操作...');

  if (!savedMetadataHex) {
    console.error('错误: 元数据尚未获取。请先运行 fetchAndSaveMetadata()。');
    return;
  }

  // --- A. 初始化解码/编码工具 ---
  // 1. 创建一个空的类型注册表
  const registry = new TypeRegistry();
  
  // 2. 使用保存的元数据字符串创建 Metadata 实例
  const metadata = new Metadata(registry, savedMetadataHex);
  
  // 3. 将元数据加载到注册表中，让它知道所有链上的类型定义
  registry.setMetadata(metadata);

  console.log('🛠️  成功使用已保存的元数据初始化了 Registry 和 Metadata。');


  // --- B. 编码: 生成 Storage Key ---
  // 这是手动生成 key 的方式，稍微复杂一些
  // 1. 获取 Pallet 和 Storage 的名称
  const palletName = 'System';
  const storageName = 'Account';
  
  // 2. 使用 xxhash 算法计算出 key 的前缀
  // 'System' -> xxhash(128) -> 0x26aa394eea5630e07c48ae0c9558cef7
  // 'Account' -> xxhash(128) -> 0xb99d880ec681799c0cf30e8886371da9
  const palletHash = xxhashAsHex(palletName, 128);
  const storageHash = xxhashAsHex(storageName, 128);
  const keyPrefix = palletHash + storageHash.substring(2); // 去掉 '0x'

  // 3. 编码地址参数
  const encodedAddress = registry.createType('AccountId', anAddress).toHex();

  // 4. 拼接成完整的 Storage Key
  const storageKey = keyPrefix + encodedAddress.substring(2);

  console.log('🔑 手动生成的 Storage Key:', storageKey);

  
  // --- C. 解码: 解析一个模拟的 RPC 响应 ---
  // 假设我们已经发送了请求并得到了这个结果
  const rpcResultHex = '0x000000000000000001000000000000000000000000000000d007000000000000000000000000000000000000000000000000000000000000';
  
  // 使用我们配置好的 registry 来解码
  // 我们从元数据中知道 System.Account 存储的是 'AccountInfo' 类型
  const accountInfo = registry.createType('AccountInfo', rpcResultHex);

  console.log('✅ 成功解码响应!');
  console.log('🧾 解码后的人类可读格式:', accountInfo.toHuman());
  console.log(`💰 余额 (Free): ${accountInfo.data.free.toHuman()}`);
}


// --- 运行示例 ---
async function run() {
  // 首先，运行一次性操作来获取元数据
  await fetchAndSaveMetadata();
  
  // 然后，在你的应用中，你可以随时调用这个函数来处理业务，无需再次联网获取元数据
  useSavedMetadata();
}

run();


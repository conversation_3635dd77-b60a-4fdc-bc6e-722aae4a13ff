<script setup>
import { usePolkadot } from '@/hooks/usePolkadot'
import { computed, reactive } from 'vue'

const {
  store,
  formatAddress,
  copyToClipboard,
  getNetworkStatus,
  getCurrentAccount,
  hasEnoughBalance,
  disconnect,
  updateAllBalances,
  transfer,
  getLatestBlock,
  connect,
} = usePolkadot()

const simpleState = reactive({
  transferTo: '5Dc4E8DDWwZ5YTqjeitpZzDTKkZuqkkktPsfykazkgc43JNH',
  transferAmount: '0.************',
  isTransferring: false,
  latestBlock: null,
  logs: [],
  selectedNetwork: 'westend-asset-hub',
  networks: {
    'westend': { name: 'Westend Relay Chain', endpoint: 'wss://westend-rpc.polkadot.io' },
    'westend-asset-hub': { name: 'Westend Asset Hub', endpoint: 'wss://westend-asset-hub-rpc.polkadot.io' },
    'rococo': { name: 'Rococo Relay Chain', endpoint: 'wss://rococo-rpc.polkadot.io' },
    'rococo-asset-hub': { name: 'Rococo Asset Hub', endpoint: 'wss://rococo-asset-hub-rpc.polkadot.io' },
  },
})

// 添加日志
function addLog(message) {
  const timestamp = new Date().toLocaleTimeString()
  simpleState.logs.unshift(`[${timestamp}] ${message}`)
  if (simpleState.logs.length > 20) {
    simpleState.logs = simpleState.logs.slice(0, 20)
  }
}

// 连接到测试网
async function handleConnect() {
  const network = simpleState.networks[simpleState.selectedNetwork]
  addLog(`开始连接到 ${network.name}...`)

  let success
  try {
    success = await connect(network.endpoint)
  }
  finally {
    console.log('%c [ success ]', 'font-size:13px; background:pink; color:#bf2c9f;', success)
  }
}

// 计算属性
const networkStatus = computed(() => getNetworkStatus())
const currentAccount = computed(() => getCurrentAccount())

// 切换网络
async function handleNetworkChange() {
  if (networkStatus.value.isConnected) {
    addLog('⚠️ 请先断开当前连接再切换网络')
    return
  }

  const network = simpleState.networks[simpleState.selectedNetwork]
  addLog(`🔄 已选择网络: ${network.name}`)
}

// 断开连接
async function handleDisconnect() {
  await disconnect()
  addLog('🔌 已断开连接')
}

// 刷新余额
async function handleRefreshBalances() {
  addLog('🔄 刷新账户余额...')
  await updateAllBalances()
  addLog('✅ 余额刷新完成')
}

// 发送转账
async function handleTransfer() {
  if (!simpleState.transferTo || !simpleState.transferAmount) {
    addLog('❌ 请填写完整的转账信息')
    return
  }

  if (!hasEnoughBalance(simpleState.transferAmount)) {
    addLog('❌ 余额不足')
    return
  }

  try {
    simpleState.isTransferring = true
    addLog(`💸 开始转账 ${simpleState.transferAmount} 到 ${formatAddress(simpleState.transferTo)}`)

    // 直接传递用户输入的金额，让 transfer 函数内部处理转换
    const hash = await transfer(simpleState.transferTo, simpleState.transferAmount)

    addLog(`✅ 转账已提交，交易哈希: ${hash.slice(0, 10)}...`)
    simpleState.transferTo = ''
    simpleState.transferAmount = ''
  }
  catch (error) {
    addLog(`❌ 转账失败: ${error.message}`)
  }
  finally {
    simpleState.isTransferring = false
  }
}

async function handleGetBlock() {
  addLog('📦 获取最新区块信息...')
  simpleState.latestBlock = await getLatestBlock()
  if (simpleState.latestBlock) {
    addLog(`✅ 最新区块: #${simpleState.latestBlock.number}`)
  }
}

async function handleCopyAddress() {
  const account = getCurrentAccount()
  if (account) {
    const success = await copyToClipboard(account.address)
    if (success) {
      addLog('📋 地址已复制到剪贴板')
    }
  }
}
</script>

<template>
  <div class="min-h-screen from-indigo-900 via-purple-900 to-pink-900 bg-gradient-to-br p-6 text-white">
    <div class="mx-auto max-w-4xl">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div class="space-y-6">
          <div class="border border-white/20 rounded-xl bg-white/10 p-6 backdrop-blur-lg">
            <h2 class="mb-4 flex items-center text-xl font-bold">
              <span class="mr-2">🌐</span>
              网络选择
            </h2>

            <div class="space-y-3">
              <div>
                <label class="mb-2 block text-sm font-medium">选择网络:</label>
                <select
                  v-model="simpleState.selectedNetwork"
                  :disabled="networkStatus.isConnected"
                  class="w-full border border-white/30 rounded-lg bg-white/20 p-3 text-white disabled:bg-gray-600"
                  @change="handleNetworkChange"
                >
                  <option
                    v-for="(network, key) in simpleState.networks"
                    :key="key"
                    :value="key"
                    class="bg-gray-800 text-white"
                  >
                    {{ network.name }}
                  </option>
                </select>
              </div>

              <div class="flex items-center justify-between">
                <span>连接状态:</span>
                <div class="flex items-center space-x-2">
                  <div
                    :class="networkStatus.isConnected ? 'bg-green-500' : 'bg-red-500'"
                    class="h-2 w-2 rounded-full"
                  />
                  <span>{{ networkStatus.isConnected ? '已连接' : '未连接' }}</span>
                </div>
              </div>

              <div v-if="networkStatus.chainName" class="flex items-center justify-between">
                <span>链名称:</span>
                <span>{{ networkStatus.chainName }}</span>
              </div>

              <div v-if="networkStatus.tokenSymbol" class="flex items-center justify-between">
                <span>代币:</span>
                <span>{{ networkStatus.tokenSymbol }}</span>
              </div>
            </div>

            <div class="mt-4 space-y-2">
              <button
                v-if="!networkStatus.isConnected"
                :disabled="networkStatus.isConnecting"
                class="w-full rounded-lg bg-green-600 px-4 py-2 transition-colors disabled:bg-gray-600 hover:bg-green-700"
                @click="handleConnect"
              >
                {{ networkStatus.isConnecting ? '连接中...' : `🔌 连接到 ${simpleState.networks[simpleState.selectedNetwork].name}` }}
              </button>

              <button
                v-else
                class="w-full rounded-lg bg-red-600 px-4 py-2 transition-colors hover:bg-red-700"
                @click="handleDisconnect"
              >
                🔌 断开连接
              </button>
            </div>
          </div>

          <div v-if="currentAccount" class="border border-white/20 rounded-xl bg-white/10 p-6 backdrop-blur-lg">
            <h2 class="mb-4 flex items-center text-xl font-bold">
              <span class="mr-2">👤</span>
              当前账户
            </h2>

            <div class="space-y-3">
              <div>
                <div class="text-sm text-gray-300">
                  地址:
                </div>
                <div class="break-all text-sm font-mono">
                  {{ currentAccount.formattedAddress }}
                </div>
                <button
                  class="mt-1 text-sm text-blue-400 hover:text-blue-300"
                  @click="handleCopyAddress"
                >
                  📋 复制完整地址
                </button>
              </div>

              <div>
                <div class="text-sm text-gray-300">
                  余额:
                </div>
                <div class="text-lg font-bold">
                  {{ currentAccount.formattedBalance }}
                </div>
              </div>
            </div>

            <div class="mt-4 space-y-2">
              <button
                class="w-full rounded-lg bg-blue-600 px-4 py-2 transition-colors hover:bg-blue-700"
                @click="handleRefreshBalances"
              >
                🔄 刷新余额
              </button>
            </div>
          </div>

          <div v-if="networkStatus.isReady" class="border border-white/20 rounded-xl bg-white/10 p-6 backdrop-blur-lg">
            <h2 class="mb-4 flex items-center text-xl font-bold">
              <span class="mr-2">💸</span>
              快速转账
            </h2>

            <div class="space-y-4">
              <div>
                <label class="mb-2 block text-sm font-medium">接收地址:</label>
                <input
                  v-model="simpleState.transferTo"
                  placeholder="输入接收方地址"
                  class="w-full border border-white/30 rounded-lg bg-white/20 p-3 text-white placeholder-gray-400"
                >
              </div>

              <div>
                <label class="mb-2 block text-sm font-medium">
                  金额 ({{ networkStatus.tokenSymbol }}):
                </label>
                <input
                  v-model="simpleState.transferAmount"
                  type="number"
                  step="0.************"
                  placeholder="输入转账金额"
                  class="w-full border border-white/30 rounded-lg bg-white/20 p-3 text-white placeholder-gray-400"
                >
              </div>

              <button
                :disabled="simpleState.isTransferring || !simpleState.transferTo || !simpleState.transferAmount"
                class="w-full rounded-lg bg-green-600 px-4 py-2 transition-colors disabled:bg-gray-600 hover:bg-green-700"
                @click="handleTransfer"
              >
                {{ simpleState.isTransferring ? '发送中...' : '💸 发送转账' }}
              </button>
            </div>
          </div>

          <div v-if="networkStatus.isReady" class="border border-white/20 rounded-xl bg-white/10 p-6 backdrop-blur-lg">
            <h2 class="mb-4 flex items-center text-xl font-bold">
              <span class="mr-2">📦</span>
              区块信息
            </h2>

            <button
              class="mb-4 w-full rounded-lg bg-purple-600 px-4 py-2 transition-colors hover:bg-purple-700"
              @click="handleGetBlock"
            >
              📦 获取最新区块
            </button>

            <div v-if="simpleState.latestBlock" class="text-sm space-y-2">
              <div class="flex justify-between">
                <span>区块号:</span>
                <span>{{ simpleState.latestBlock.number }}</span>
              </div>
              <div class="flex justify-between">
                <span>交易数:</span>
                <span>{{ simpleState.latestBlock.extrinsicsCount }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="border border-white/20 rounded-xl bg-white/10 p-6 backdrop-blur-lg">
          <h2 class="mb-4 flex items-center text-xl font-bold">
            <span class="mr-2">📝</span>
            操作日志
          </h2>

          <div class="h-96 overflow-y-auto rounded-lg bg-black/30 p-4">
            <div v-if="simpleState.logs.length === 0" class="text-center text-gray-400">
              暂无日志记录
            </div>
            <div
              v-for="(log, index) in simpleState.logs"
              :key="index"
              class="mb-2 text-sm font-mono"
            >
              {{ log }}
            </div>
          </div>

          <button
            class="mt-4 w-full rounded-lg bg-gray-600 px-4 py-2 transition-colors hover:bg-gray-700"
            @click="simpleState.logs = []"
          >
            🗑️ 清空日志
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

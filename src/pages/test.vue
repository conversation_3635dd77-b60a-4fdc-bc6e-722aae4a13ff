<script setup>
import { ApiPromise, WsProvider } from '@polkadot/api'



 // Initialise the provider to connect to the local node
 const provider = new WsProvider('ws://************:9944');

// Create the API and wait until ready
const api = await ApiPromise.create({ provider });
const [chain, nodeName, nodeVersion] = await Promise.all([
    api.rpc.system.chain(),
    api.rpc.system.name(),
    api.rpc.system.version()
  ]);

  console.log(`You are connected to chain ${chain} using ${nodeName} v${nodeVersion}`);
</script> 
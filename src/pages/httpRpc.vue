<script setup>
import { reactive, computed, onMounted } from 'vue'
import { ApiPromise, HttpProvider } from '@polkadot/api'
import { Keyring } from '@polkadot/keyring'
import { cryptoWaitReady } from '@polkadot/util-crypto'

// HTTP RPC 配置
const HTTP_RPC_URL = 'http://127.0.0.1:9933'
const TEST_MNEMONIC = 'scan slow alter service unhappy capital acoustic picnic grant affair leader curtain'

// 状态管理
const httpRpcState = reactive({
  // 连接状态
  api: null,
  isConnected: false,
  isConnecting: false,
  connectionError: null,
  
  // 链信息
  chainInfo: {
    name: '',
    version: '',
    tokenSymbol: 'DOT',
    tokenDecimals: 12
  },
  
  // 账户信息
  keyring: null,
  currentAccount: null,
  balance: '0',
  formattedBalance: '0 DOT',
  
  // 转账相关
  transferTo: '',
  transferAmount: '',
  isTransferring: false,
  lastTransactionHash: '',
  
  // 日志
  logs: []
})

// 添加日志函数
function addLog(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString()
  const logEntry = {
    timestamp,
    message,
    type
  }
  httpRpcState.logs.unshift(logEntry)
  if (httpRpcState.logs.length > 50) {
    httpRpcState.logs = httpRpcState.logs.slice(0, 50)
  }
  console.log(`[${timestamp}] ${message}`)
}

// 格式化余额显示
function formatBalance(balance, decimals = 12, symbol = 'DOT') {
  if (!balance || balance === '0') return `0 ${symbol}`
  
  try {
    const bigBalance = BigInt(balance)
    const divisor = BigInt(10 ** decimals)
    const quotient = bigBalance / divisor
    const remainder = bigBalance % divisor
    
    const decimalPart = remainder.toString().padStart(decimals, '0')
    let formattedDecimal = decimalPart.replace(/0+$/, '')
    if (formattedDecimal.length > 6) {
      formattedDecimal = formattedDecimal.slice(0, 6)
    }
    
    const result = formattedDecimal ? `${quotient}.${formattedDecimal}` : quotient.toString()
    return `${result} ${symbol}`
  } catch (error) {
    addLog(`格式化余额失败: ${error.message}`, 'error')
    return `${balance} ${symbol}`
  }
}

// 转换用户输入金额为链上单位
function toChainUnits(amount, decimals = 12) {
  try {
    const numAmount = parseFloat(amount)
    if (isNaN(numAmount) || numAmount <= 0) {
      throw new Error('无效的金额')
    }
    
    const multiplier = BigInt(10 ** decimals)
    const chainAmount = BigInt(Math.floor(numAmount * (10 ** decimals)))
    return chainAmount.toString()
  } catch (error) {
    throw new Error(`金额转换失败: ${error.message}`)
  }
}

// 连接到 HTTP RPC
async function connectToHttpRpc() {
  try {
    httpRpcState.isConnecting = true
    httpRpcState.connectionError = null
    addLog('🔄 开始连接到 HTTP RPC...')
    
    // 等待加密库准备就绪
    await cryptoWaitReady()
    addLog('✅ 加密库准备完成')
    
    // 创建 HTTP Provider
    addLog(`🌐 创建 HTTP Provider: ${HTTP_RPC_URL}`)
    const provider = new HttpProvider(HTTP_RPC_URL)
    
    // 创建 API 实例
    addLog('🔧 创建 API 实例...')
    httpRpcState.api = await ApiPromise.create({ 
      provider,
      throwOnConnect: false,
      throwOnUnknown: false,
      noInitWarn: true
    })
    
    await httpRpcState.api.isReady
    addLog('✅ API 实例创建成功')
    
    // 获取链信息
    await getChainInfo()
    
    // 初始化密钥环
    await initKeyring()
    
    httpRpcState.isConnected = true
    addLog('🎉 HTTP RPC 连接建立成功', 'success')
    
  } catch (error) {
    httpRpcState.connectionError = error.message
    addLog(`❌ 连接失败: ${error.message}`, 'error')
    console.error('连接错误:', error)
  } finally {
    httpRpcState.isConnecting = false
  }
}

// 获取链信息
async function getChainInfo() {
  try {
    addLog('📋 获取链信息...')
    
    const [chain, version, properties] = await Promise.all([
      httpRpcState.api.rpc.system.chain(),
      httpRpcState.api.rpc.system.version(),
      httpRpcState.api.rpc.system.properties()
    ])
    
    httpRpcState.chainInfo = {
      name: chain.toString(),
      version: version.toString(),
      tokenSymbol: properties.tokenSymbol?.toHuman()?.[0] || 'DOT',
      tokenDecimals: properties.tokenDecimals?.toHuman()?.[0] || 12
    }
    
    addLog(`✅ 链信息: ${httpRpcState.chainInfo.name} (${httpRpcState.chainInfo.tokenSymbol})`)
  } catch (error) {
    addLog(`⚠️ 获取链信息失败: ${error.message}`, 'error')
    // 使用默认值
    httpRpcState.chainInfo = {
      name: '本地测试链',
      version: '未知',
      tokenSymbol: 'DOT',
      tokenDecimals: 12
    }
  }
}

// 初始化密钥环
async function initKeyring() {
  try {
    addLog('🔑 初始化密钥环...')
    
    httpRpcState.keyring = new Keyring({ type: 'sr25519' })
    httpRpcState.currentAccount = httpRpcState.keyring.addFromMnemonic(TEST_MNEMONIC)
    
    addLog(`✅ 账户地址: ${httpRpcState.currentAccount.address}`)
    
    // 查询余额
    await queryBalance()
    
  } catch (error) {
    addLog(`❌ 密钥环初始化失败: ${error.message}`, 'error')
  }
}

// 查询余额
async function queryBalance() {
  if (!httpRpcState.api || !httpRpcState.currentAccount) {
    addLog('❌ API 或账户未准备就绪', 'error')
    return
  }
  
  try {
    addLog('🔍 查询账户余额...')
    
    const accountInfo = await httpRpcState.api.query.system.account(httpRpcState.currentAccount.address)
    
    if (accountInfo && accountInfo.data) {
      httpRpcState.balance = accountInfo.data.free.toString()
      httpRpcState.formattedBalance = formatBalance(
        httpRpcState.balance,
        httpRpcState.chainInfo.tokenDecimals,
        httpRpcState.chainInfo.tokenSymbol
      )
      addLog(`💰 当前余额: ${httpRpcState.formattedBalance}`)
    } else {
      httpRpcState.balance = '0'
      httpRpcState.formattedBalance = `0 ${httpRpcState.chainInfo.tokenSymbol}`
      addLog('💰 账户余额: 0')
    }
  } catch (error) {
    addLog(`❌ 查询余额失败: ${error.message}`, 'error')
  }
}

// 发送转账
async function sendTransfer() {
  if (!httpRpcState.api || !httpRpcState.currentAccount) {
    addLog('❌ API 或账户未准备就绪', 'error')
    return
  }
  
  if (!httpRpcState.transferTo || !httpRpcState.transferAmount) {
    addLog('❌ 请填写完整的转账信息', 'error')
    return
  }

  if (!isValidAddress(httpRpcState.transferTo)) {
    addLog('❌ 接收地址格式无效', 'error')
    return
  }

  if (!hasEnoughBalance(httpRpcState.transferAmount)) {
    addLog('❌ 余额不足', 'error')
    return
  }
  
  try {
    httpRpcState.isTransferring = true
    addLog(`💸 开始转账 ${httpRpcState.transferAmount} ${httpRpcState.chainInfo.tokenSymbol} 到 ${httpRpcState.transferTo.slice(0, 8)}...`)
    
    // 转换金额
    const chainAmount = toChainUnits(httpRpcState.transferAmount, httpRpcState.chainInfo.tokenDecimals)
    
    // 创建转账交易
    const transfer = httpRpcState.api.tx.balances.transferKeepAlive(httpRpcState.transferTo, chainAmount)
    
    // 签名并发送 (HTTP 模式下只能获取交易哈希，无法跟踪状态)
    const hash = await transfer.signAndSend(httpRpcState.currentAccount)
    
    httpRpcState.lastTransactionHash = hash.toHex()
    addLog(`✅ 转账已提交，交易哈希: ${httpRpcState.lastTransactionHash}`, 'success')
    addLog('⚠️ HTTP 模式无法跟踪交易状态，请手动查询确认', 'warning')
    
    // 清空表单
    httpRpcState.transferTo = ''
    httpRpcState.transferAmount = ''
    
    // 延迟查询余额更新
    setTimeout(() => {
      queryBalance()
    }, 3000)
    
  } catch (error) {
    addLog(`❌ 转账失败: ${error.message}`, 'error')
  } finally {
    httpRpcState.isTransferring = false
  }
}

// 断开连接
async function disconnect() {
  try {
    if (httpRpcState.api) {
      await httpRpcState.api.disconnect()
      httpRpcState.api = null
    }
    
    httpRpcState.isConnected = false
    httpRpcState.currentAccount = null
    httpRpcState.balance = '0'
    httpRpcState.formattedBalance = '0 DOT'
    httpRpcState.lastTransactionHash = ''
    
    addLog('🔌 已断开 HTTP RPC 连接')
  } catch (error) {
    addLog(`❌ 断开连接失败: ${error.message}`, 'error')
  }
}

// 计算属性
const isReady = computed(() => httpRpcState.isConnected && httpRpcState.currentAccount)
const shortAddress = computed(() => {
  if (!httpRpcState.currentAccount) return ''
  const addr = httpRpcState.currentAccount.address
  return `${addr.slice(0, 6)}...${addr.slice(-4)}`
})

// 复制到剪贴板
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text)
    addLog('📋 已复制到剪贴板', 'success')
    return true
  } catch (error) {
    addLog(`❌ 复制失败: ${error.message}`, 'error')
    return false
  }
}

// 验证地址格式
function isValidAddress(address) {
  const ss58Regex = /^[1-9A-HJ-NP-Za-km-z]{47,48}$/
  return ss58Regex.test(address)
}

// 检查余额是否足够
function hasEnoughBalance(amount) {
  try {
    const chainAmount = toChainUnits(amount, httpRpcState.chainInfo.tokenDecimals)
    const currentBalance = BigInt(httpRpcState.balance)
    const transferAmount = BigInt(chainAmount)

    // 预留一些余额作为手续费
    const feeBuffer = BigInt(10 ** (httpRpcState.chainInfo.tokenDecimals - 3)) // 0.001 token

    return currentBalance > transferAmount + feeBuffer
  } catch {
    return false
  }
}

// 获取区块信息
async function getLatestBlock() {
  if (!httpRpcState.api) {
    addLog('❌ API 未准备就绪', 'error')
    return null
  }

  try {
    addLog('📦 获取最新区块信息...')

    const [hash, header] = await Promise.all([
      httpRpcState.api.rpc.chain.getBlockHash(),
      httpRpcState.api.rpc.chain.getHeader()
    ])

    const blockInfo = {
      number: header.number.toNumber(),
      hash: hash.toString(),
      parentHash: header.parentHash.toString(),
      timestamp: Date.now()
    }

    addLog(`✅ 最新区块: #${blockInfo.number}`, 'success')
    return blockInfo

  } catch (error) {
    addLog(`❌ 获取区块信息失败: ${error.message}`, 'error')
    return null
  }
}

// 组件挂载时的初始化
onMounted(() => {
  addLog('🚀 HTTP RPC 组件已加载')
  addLog('💡 提示: HTTP 模式适合简单查询，但无法实时跟踪交易状态', 'warning')
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 text-white">
    <div class="mx-auto max-w-6xl">
      <!-- 页面标题 -->
      <div class="mb-8 text-center">
        <h1 class="text-4xl font-bold text-white mb-2">
          🌐 Polkadot HTTP RPC 客户端
        </h1>
        <p class="text-slate-300">
          基于 HTTP Provider 的 Polkadot.js API 实现
        </p>
      </div>

      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- 左侧：连接和账户信息 -->
        <div class="space-y-6">
          <!-- 连接控制 -->
          <div class="rounded-xl border border-slate-700 bg-slate-800/50 p-6 backdrop-blur-sm">
            <h2 class="mb-4 flex items-center text-xl font-semibold">
              <span class="mr-2">🔗</span>
              HTTP RPC 连接
            </h2>
            
            <div class="space-y-4">
              <div class="rounded-lg bg-slate-900/50 p-3">
                <div class="text-sm text-slate-400">RPC 端点:</div>
                <div class="font-mono text-sm">{{ HTTP_RPC_URL }}</div>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-slate-300">连接状态:</span>
                <div class="flex items-center space-x-2">
                  <div
                    :class="httpRpcState.isConnected ? 'bg-green-500' : 'bg-red-500'"
                    class="h-2 w-2 rounded-full"
                  />
                  <span>{{ httpRpcState.isConnected ? '已连接' : '未连接' }}</span>
                </div>
              </div>
              
              <div v-if="httpRpcState.chainInfo.name" class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-slate-400">链名称:</span>
                  <span>{{ httpRpcState.chainInfo.name }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">代币符号:</span>
                  <span>{{ httpRpcState.chainInfo.tokenSymbol }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-400">精度:</span>
                  <span>{{ httpRpcState.chainInfo.tokenDecimals }}</span>
                </div>
              </div>
              
              <div v-if="httpRpcState.connectionError" class="rounded-lg bg-red-900/30 border border-red-700 p-3">
                <div class="text-sm text-red-300">
                  ❌ {{ httpRpcState.connectionError }}
                </div>
              </div>
              
              <div class="space-y-2">
                <button
                  v-if="!httpRpcState.isConnected"
                  :disabled="httpRpcState.isConnecting"
                  class="w-full rounded-lg bg-blue-600 px-4 py-2 font-medium transition-colors disabled:bg-slate-600 hover:bg-blue-700"
                  @click="connectToHttpRpc"
                >
                  {{ httpRpcState.isConnecting ? '连接中...' : '🔌 连接到 HTTP RPC' }}
                </button>
                
                <button
                  v-else
                  class="w-full rounded-lg bg-red-600 px-4 py-2 font-medium transition-colors hover:bg-red-700"
                  @click="disconnect"
                >
                  🔌 断开连接
                </button>
              </div>
            </div>
          </div>

          <!-- 账户信息 -->
          <div v-if="httpRpcState.currentAccount" class="rounded-xl border border-slate-700 bg-slate-800/50 p-6 backdrop-blur-sm">
            <h2 class="mb-4 flex items-center text-xl font-semibold">
              <span class="mr-2">👤</span>
              账户信息
            </h2>
            
            <div class="space-y-4">
              <div>
                <div class="text-sm text-slate-400 mb-1">地址:</div>
                <div class="rounded-lg bg-slate-900/50 p-3 font-mono text-sm break-all">
                  {{ httpRpcState.currentAccount.address }}
                </div>
                <div class="mt-1 text-xs text-slate-500">
                  简短格式: {{ shortAddress }}
                </div>
                <button
                  class="mt-2 text-sm text-blue-400 hover:text-blue-300 transition-colors"
                  @click="copyToClipboard(httpRpcState.currentAccount.address)"
                >
                  📋 复制完整地址
                </button>
              </div>
              
              <div>
                <div class="text-sm text-slate-400 mb-1">余额:</div>
                <div class="text-2xl font-bold text-green-400">
                  {{ httpRpcState.formattedBalance }}
                </div>
              </div>
              
              <div class="grid grid-cols-2 gap-2">
                <button
                  :disabled="!isReady"
                  class="rounded-lg bg-green-600 px-4 py-2 font-medium transition-colors disabled:bg-slate-600 hover:bg-green-700"
                  @click="queryBalance"
                >
                  🔄 刷新余额
                </button>

                <button
                  :disabled="!isReady"
                  class="rounded-lg bg-blue-600 px-4 py-2 font-medium transition-colors disabled:bg-slate-600 hover:bg-blue-700"
                  @click="getLatestBlock"
                >
                  📦 获取区块
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：转账和日志 -->
        <div class="space-y-6">
          <!-- 转账功能 -->
          <div v-if="isReady" class="rounded-xl border border-slate-700 bg-slate-800/50 p-6 backdrop-blur-sm">
            <h2 class="mb-4 flex items-center text-xl font-semibold">
              <span class="mr-2">💸</span>
              发送转账
            </h2>
            
            <div class="space-y-4">
              <div>
                <label class="mb-2 block text-sm font-medium text-slate-300">接收地址:</label>
                <input
                  v-model="httpRpcState.transferTo"
                  placeholder="输入接收方地址"
                  class="w-full rounded-lg border p-3 text-white placeholder-slate-500 focus:outline-none transition-colors"
                  :class="httpRpcState.transferTo && !isValidAddress(httpRpcState.transferTo)
                    ? 'border-red-500 bg-red-900/20 focus:border-red-400'
                    : 'border-slate-600 bg-slate-900/50 focus:border-blue-500'"
                >
                <div v-if="httpRpcState.transferTo && !isValidAddress(httpRpcState.transferTo)" class="mt-1 text-sm text-red-400">
                  ❌ 地址格式无效
                </div>
              </div>
              
              <div>
                <label class="mb-2 block text-sm font-medium text-slate-300">
                  金额 ({{ httpRpcState.chainInfo.tokenSymbol }}):
                </label>
                <input
                  v-model="httpRpcState.transferAmount"
                  type="number"
                  step="0.000001"
                  placeholder="输入转账金额"
                  class="w-full rounded-lg border p-3 text-white placeholder-slate-500 focus:outline-none transition-colors"
                  :class="httpRpcState.transferAmount && !hasEnoughBalance(httpRpcState.transferAmount)
                    ? 'border-red-500 bg-red-900/20 focus:border-red-400'
                    : 'border-slate-600 bg-slate-900/50 focus:border-blue-500'"
                >
                <div v-if="httpRpcState.transferAmount && !hasEnoughBalance(httpRpcState.transferAmount)" class="mt-1 text-sm text-red-400">
                  ❌ 余额不足 (当前: {{ httpRpcState.formattedBalance }})
                </div>
              </div>
              
              <div v-if="httpRpcState.lastTransactionHash" class="rounded-lg bg-blue-900/30 border border-blue-700 p-3">
                <div class="text-sm text-blue-300 mb-1">最近交易哈希:</div>
                <div class="font-mono text-xs break-all">
                  {{ httpRpcState.lastTransactionHash }}
                </div>
              </div>
              
              <button
                :disabled="httpRpcState.isTransferring
                          || !httpRpcState.transferTo
                          || !httpRpcState.transferAmount
                          || !isValidAddress(httpRpcState.transferTo)
                          || !hasEnoughBalance(httpRpcState.transferAmount)"
                class="w-full rounded-lg bg-purple-600 px-4 py-2 font-medium transition-colors disabled:bg-slate-600 hover:bg-purple-700"
                @click="sendTransfer"
              >
                {{ httpRpcState.isTransferring ? '发送中...' : '💸 发送转账' }}
              </button>
              
              <div class="rounded-lg bg-yellow-900/30 border border-yellow-700 p-3">
                <div class="text-sm text-yellow-300">
                  ⚠️ HTTP 模式限制：无法实时跟踪交易状态，只能获取交易哈希
                </div>
              </div>
            </div>
          </div>

          <!-- 操作日志 -->
          <div class="rounded-xl border border-slate-700 bg-slate-800/50 p-6 backdrop-blur-sm">
            <h2 class="mb-4 flex items-center text-xl font-semibold">
              <span class="mr-2">📝</span>
              操作日志
            </h2>
            
            <div class="h-80 overflow-y-auto rounded-lg bg-slate-900/50 p-4">
              <div v-if="httpRpcState.logs.length === 0" class="text-center text-slate-500">
                暂无日志记录
              </div>
              <div
                v-for="(log, index) in httpRpcState.logs"
                :key="index"
                :class="{
                  'text-red-400': log.type === 'error',
                  'text-green-400': log.type === 'success',
                  'text-yellow-400': log.type === 'warning',
                  'text-slate-300': log.type === 'info'
                }"
                class="mb-2 text-sm font-mono"
              >
                <span class="text-slate-500">[{{ log.timestamp }}]</span>
                {{ log.message }}
              </div>
            </div>
            
            <button
              class="mt-4 w-full rounded-lg bg-slate-600 px-4 py-2 font-medium transition-colors hover:bg-slate-700"
              @click="httpRpcState.logs = []"
            >
              🗑️ 清空日志
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

import { ApiPromise, WsProvider } from '@polkadot/api'
import { u8aToHex, hexToU8a, isHex } from '@polkadot/util';
import { decodeAddress, encodeAddress } from '@polkadot/keyring';
import { blake2AsHex } from '@polkadot/util-crypto';



 // Initialise the provider to connect to the local node
 const provider = new WsProvider('ws://127.0.0.1:9944');

// Create the API and wait until ready
const api = await ApiPromise.create({ provider });
const [chain, nodeName, nodeVersion] = await Promise.all([
    api.rpc.system.chain(),
    api.rpc.system.name(),
    api.rpc.system.version()
  ]);

  console.log(`You are connected to chain ${chain} using ${nodeName} v${nodeVersion}`);

// 查询账户余额
async function queryBalance(address) {
  try {
    console.log(`🔍 查询账户余额: ${address}`);
    
    // 使用 system.account 查询账户信息
    const accountInfo = await api.query.system.account(address);
    
    console.log('%c [  ]', 'font-size:13px; background:pink; color:#bf2c9f;', api.query.system )
    // 提取余额数据
    const free = accountInfo.data.free.toString();
    const reserved = accountInfo.data.reserved.toString();
    const frozen = accountInfo.data.frozen.toString();
    
    console.log(`💰 账户 ${address.slice(0, 8)}... 详细余额:`);
    console.log(`  - 可用余额: ${free}`);
    console.log(`  - 保留余额: ${reserved}`);
    console.log(`  - 冻结余额: ${frozen}`);
    
    // 
    const formattedBalance = formatBalanceDisplay(free);
    console.log(`  - 格式化可用余额: ${formattedBalance}`);
    
    return {
      free,
      reserved,
      frozen,
      formatted: formattedBalance
    };
  } catch (error) {
    console.error('❌ 获取余额失败:', error);
    return null;
  }
}

// 格式化余额显示
function formatBalanceDisplay(balance, decimals = 12, symbol = 'DOT') {
  if (!balance || balance === '0') return `0 ${symbol}`;

  try {
    const bigBalance = BigInt(balance);
    const divisor = BigInt(10 ** decimals);
    const quotient = bigBalance / divisor;
    const remainder = bigBalance % divisor;

    // 格式化小数部分
    const decimalPart = remainder.toString().padStart(decimals, '0');
    let formattedDecimal = decimalPart.replace(/0+$/, ''); // 移除尾随零
    if (formattedDecimal.length > 4) {
      formattedDecimal = formattedDecimal.slice(0, 4); // 最多显示4位小数
    }

    const result = formattedDecimal ? `${quotient}.${formattedDecimal}` : quotient.toString();
    return `${result} ${symbol}`;
  } catch (error) {
    console.error('格式化余额失败:', error);
    return `${balance} ${symbol}`;
  }
}

// 测试账户地址 - 替换为你想查询的地址
// const testAddress = '5Dc4E8DDWwZ5YTqjeitpZzDTKkZuqkkktPsfykazkgc43JNH';
const testAddress = '5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY';

// 执行查询
const balance = await queryBalance(testAddress);
console.log('查询结果:', balance);

// 自定义序列化流程
function customSerializeAccountQuery(address) {
  // 1. 将地址解码为字节数组
  const addressBytes = isHex(address) 
    ? hexToU8a(address) 
    : decodeAddress(address);
  
  // 2. 构造存储键前缀 (System.Account)
  const storageKeyPrefix = '0x26aa394eea5630e07c48ae0c9558cef7b99d880ec681799c0cf30e8886371da9';
  
  // 3. 对地址进行 blake2 哈希
  const addressHash = blake2AsHex(addressBytes, 128);
  
  // 4. 构造完整的存储键
  const fullStorageKey = `${storageKeyPrefix}${addressHash.slice(2)}`;
  
  return fullStorageKey;
}
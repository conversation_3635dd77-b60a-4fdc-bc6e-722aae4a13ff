import { defineStore } from 'pinia'
import { Api<PERSON>rom<PERSON>, Ws<PERSON>rovider } from '@polkadot/api'
import { Keyring } from '@polkadot/keyring'
import { cryptoWaitReady } from '@polkadot/util-crypto'

// Polkadot 测试网络端点
const ENDPOINTS = {
  westend: 'wss://westend-rpc.polkadot.io',
  rococo: 'wss://rococo-rpc.polkadot.io',
  kusama: 'wss://kusama-rpc.polkadot.io',
  polkadot: 'wss://rpc.polkadot.io',
}

// 测试账户助记词 (官方测试用)
const TEST_MNEMONIC = 'bottom drive obey lake curtain smoke basket hold race lonely fit walk'

export const usePolkadotStore = defineStore('polkadot', {
  state: () => ({
    api: null,
    isConnected: false,
    isConnecting: false,
    currentEndpoint: ENDPOINTS.westend,
    keyring: null,
    accounts: [],
    selectedAccount: null,
    chainInfo: null,
    balances: {},
    transactions: [],
    error: null,
  }),

  getters: {
    isReady: (state) => state.api && state.isConnected && state.keyring,
    currentBalance: (state) => {
      if (!state.selectedAccount) return '0'
      return state.balances[state.selectedAccount.address] || '0'
    },
  },

  actions: {
    // 连接到 Polkadot 网络
    async connect(endpoint) {
      try {
        this.isConnecting = true
        this.error = null

        if (endpoint) {
          this.currentEndpoint = endpoint
        }

        console.log('🔄 开始连接到:', this.currentEndpoint)

        // 等待加密库准备就绪
        console.log('⏳ 等待加密库准备就绪...')
        await cryptoWaitReady()
        console.log('✅ 加密库准备完成')

        // 创建 WebSocket 提供者，增加超时时间
        console.log('🌐 创建 WebSocket 连接...')
        const provider = new WsProvider(this.currentEndpoint, 5000) // 5秒超时

        // 添加连接超时处理
        const connectionTimeout = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('连接超时')), 15000) // 15秒超时
        })

        // 创建 API 实例
        console.log('🔧 创建 API 实例...')
        const apiCreation = ApiPromise.create({
          provider,
          throwOnConnect: false, // 不在连接时抛出异常
          throwOnUnknown: false, // 不在遇到未知类型时抛出异常
        })

        this.api = await Promise.race([apiCreation, connectionTimeout])

        // 等待 API 准备就绪
        console.log('⏳ 等待 API 准备就绪...')
        await Promise.race([this.api.isReady, connectionTimeout])
        console.log('✅ API 实例创建成功')

        // 监听连接状态
        provider.on('connected', () => {
          this.isConnected = true
          console.log('✅ WebSocket 连接已建立')
        })

        provider.on('disconnected', () => {
          this.isConnected = false
          console.log('❌ WebSocket 连接已断开')
        })

        provider.on('error', (error) => {
          console.error('WebSocket 错误:', error)
          this.error = `WebSocket 错误: ${error.message}`
        })

        // 获取链信息
        console.log('📋 获取链信息...')
        await this.getChainInfo()

        // 初始化密钥环
        console.log('🔑 初始化密钥环...')
        await this.initKeyring()

        this.isConnected = true
        console.log('🎉 Polkadot 连接完全建立')

      } catch (error) {
        this.error = `连接失败: ${error instanceof Error ? error.message : String(error)}`
        console.error('❌ 连接过程中发生错误:', error)

        // 清理资源
        if (this.api) {
          try {
            await this.api.disconnect()
          } catch (e) {
            console.warn('断开连接时出错:', e)
          }
          this.api = null
        }
        this.isConnected = false
      } finally {
        this.isConnecting = false
      }
    },

    // 断开连接
    async disconnect() {
      if (this.api) {
        await this.api.disconnect()
        this.api = null
      }
      this.isConnected = false
      this.chainInfo = null
      this.accounts = []
      this.selectedAccount = null
      this.balances = {}
      console.log('🔌 已断开 Polkadot 连接')
    },

    // 获取链信息
    async getChainInfo() {
      if (!this.api) return

      try {
        console.log('📋 开始获取链信息...')

        // 分步获取信息，避免一次性获取导致的错误
        const chain = await this.api.rpc.system.chain()
        const version = await this.api.rpc.system.version()

        console.log('✅ 基础链信息获取成功')

        // 根据链名称直接设置默认值，避免复杂的属性解析
        const chainName = chain.toString().toLowerCase()
        let tokenSymbol = 'DOT'
        let tokenDecimals = 12

        // 根据已知的链设置正确的代币信息
        if (chainName.includes('westend')) {
          tokenSymbol = 'WND'
          tokenDecimals = 12
        } else if (chainName.includes('rococo')) {
          tokenSymbol = 'ROC'
          tokenDecimals = 12
        } else if (chainName.includes('kusama')) {
          tokenSymbol = 'KSM'
          tokenDecimals = 12
        } else if (chainName.includes('polkadot')) {
          tokenSymbol = 'DOT'
          tokenDecimals = 10
        }

        // 尝试获取链属性，但不依赖它们
        try {
          const properties = await this.api.rpc.system.properties()
          console.log('📊 尝试获取链属性...')

          // 非常保守的属性解析，避免 toHuman() 调用
          if (properties) {
            // 尝试获取代币符号，但不强制转换
            try {
              if (properties.tokenSymbol && typeof properties.tokenSymbol.toString === 'function') {
                const symbolStr = properties.tokenSymbol.toString()
                if (symbolStr && symbolStr !== '[object Object]') {
                  // 简单的字符串解析，避免复杂的类型转换
                  const match = symbolStr.match(/\[([A-Z]+)\]/) || symbolStr.match(/([A-Z]{2,4})/)
                  if (match && match[1]) {
                    tokenSymbol = match[1]
                  }
                }
              }
            } catch (symbolError) {
              console.warn('解析代币符号失败，使用默认值:', symbolError.message)
            }

            // 尝试获取代币精度，但不强制转换
            try {
              if (properties.tokenDecimals && typeof properties.tokenDecimals.toString === 'function') {
                const decimalsStr = properties.tokenDecimals.toString()
                if (decimalsStr && decimalsStr !== '[object Object]') {
                  // 简单的数字解析
                  const match = decimalsStr.match(/\[(\d+)\]/) || decimalsStr.match(/(\d+)/)
                  if (match && match[1]) {
                    const parsed = parseInt(match[1], 10)
                    if (!isNaN(parsed) && parsed >= 0 && parsed <= 18) {
                      tokenDecimals = parsed
                    }
                  }
                }
              }
            } catch (decimalsError) {
              console.warn('解析代币精度失败，使用默认值:', decimalsError.message)
            }
          }

          console.log('✅ 链属性解析完成（可选）')
        } catch (propertiesError) {
          console.warn('获取链属性失败，使用基于链名称的默认值:', propertiesError.message)
          // 已经根据链名称设置了默认值，所以这里不需要额外处理
        }

        this.chainInfo = {
          name: chain.toString(),
          version: version.toString(),
          tokenSymbol,
          tokenDecimals,
        }

        console.log('✅ 链信息获取成功:', this.chainInfo)
      } catch (error) {
        console.error('获取链信息失败:', error)

        // 即使在错误情况下，也尝试设置合理的默认值
        let fallbackSymbol = 'DOT'
        let fallbackDecimals = 12

        // 如果错误信息中包含链名称，尝试解析
        const errorStr = error.message || error.toString()
        if (errorStr.toLowerCase().includes('westend')) {
          fallbackSymbol = 'WND'
        } else if (errorStr.toLowerCase().includes('rococo')) {
          fallbackSymbol = 'ROC'
        } else if (errorStr.toLowerCase().includes('kusama')) {
          fallbackSymbol = 'KSM'
        }

        this.chainInfo = {
          name: '未知链',
          version: '未知版本',
          tokenSymbol: fallbackSymbol,
          tokenDecimals: fallbackDecimals,
        }
      }
    },

    // 初始化密钥环
    async initKeyring() {
      try {
        this.keyring = new Keyring({ type: 'sr25519' })
        
        // 添加测试账户
        const testAccount = this.keyring.addFromMnemonic(TEST_MNEMONIC)
        this.accounts = [testAccount]
        this.selectedAccount = testAccount
        
        console.log('🔑 密钥环初始化完成')
        console.log('测试账户地址:', testAccount.address)
        
        // 获取账户余额
        await this.updateBalance(testAccount.address)
        
      } catch (error) {
        this.error = `密钥环初始化失败: ${error instanceof Error ? error.message : String(error)}`
        console.error('密钥环初始化失败:', error)
      }
    },

    // 更新账户余额
    async updateBalance(address) {
      if (!this.api) {
        console.warn('API 未准备就绪，无法获取余额')
        return
      }

      try {
        console.log(`🔍 查询账户余额: ${address}`)

        // 使用更安全的方式查询余额
        const accountInfo = await this.api.query.system.account(address)

        // 检查返回的数据结构
        if (accountInfo && accountInfo.data) {
          const balance = accountInfo.data
          const free = balance.free ? balance.free.toString() : '0'
          this.balances[address] = free
          console.log(`💰 账户 ${address.slice(0, 8)}... 余额: ${free}`)
        } else {
          console.warn('账户信息格式异常:', accountInfo)
          this.balances[address] = '0'
        }
      } catch (error) {
        console.error('获取余额失败:', error)
        this.balances[address] = '0'

        // 如果是类型转换错误，尝试重新连接
        if (error.message && error.message.includes('asVariant')) {
          console.warn('检测到类型转换错误，可能需要重新连接')
          this.error = '数据类型不兼容，请尝试重新连接'
        }
      }
    },

    // 更新所有账户余额
    async updateAllBalances() {
      for (const account of this.accounts) {
        await this.updateBalance(account.address)
      }
    },

    // 发送转账
    async transfer(to, amount) {
      if (!this.api || !this.selectedAccount) {
        throw new Error('API 或账户未准备就绪')
      }

      try {
        const transfer = this.api.tx.balances.transferKeepAlive(to, amount)

        // 签名并发送交易
        const hash = await transfer.signAndSend(this.selectedAccount, (result) => {
          console.log('交易状态:', result.status.toString())

          if (result.status.isInBlock) {
            console.log('✅ 交易已打包到区块:', result.status.asInBlock.toString())
          } else if (result.status.isFinalized) {
            console.log('🎉 交易已确认:', result.status.asFinalized.toString())
            // 更新余额
            this.updateBalance(this.selectedAccount.address)
            this.updateBalance(to)
          }
        })

        // 记录交易
        this.transactions.unshift({
          hash: hash.toString(),
          from: this.selectedAccount.address,
          to,
          amount,
          timestamp: Date.now(),
          status: 'pending',
        })

        return hash.toString()
      } catch (error) {
        console.error('转账失败:', error)
        throw error
      }
    },

    // 获取最新区块信息
    async getLatestBlock() {
      if (!this.api) return null

      try {
        const hash = await this.api.rpc.chain.getBlockHash()
        const block = await this.api.rpc.chain.getBlock(hash)
        const header = await this.api.rpc.chain.getHeader(hash)

        return {
          number: header.number.toNumber(),
          hash: hash.toString(),
          parentHash: header.parentHash.toString(),
          extrinsicsCount: block.block.extrinsics.length,
          timestamp: Date.now(),
        }
      } catch (error) {
        console.error('获取区块信息失败:', error)
        return null
      }
    },

    // 添加新账户
    addAccount(mnemonic, name) {
      if (!this.keyring) {
        throw new Error('密钥环未初始化')
      }

      try {
        const account = this.keyring.addFromMnemonic(mnemonic, { name })
        this.accounts.push(account)
        this.updateBalance(account.address)
        return account
      } catch (error) {
        console.error('添加账户失败:', error)
        throw error
      }
    },

    // 选择账户
    selectAccount(address) {
      const account = this.accounts.find(acc => acc.address === address)
      if (account) {
        this.selectedAccount = account
        console.log('🔄 切换到账户:', address)
      }
    },

    // 清除错误
    clearError() {
      this.error = null
    },
  },
})

export { ENDPOINTS }

<script setup>
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
</script>

<template>
  <div>
    <!-- 导航栏 -->
    <nav class="bg-slate-900 border-b border-slate-700 px-6 py-4">
      <div class="mx-auto max-w-6xl flex items-center justify-between">
        <div class="flex items-center space-x-1">
          <span class="text-2xl">🔗</span>
          <h1 class="text-xl font-bold text-white">Polkadot.js 客户端</h1>
        </div>

        <div class="flex space-x-4">
          <router-link
            to="/"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-colors',
              route.path === '/'
                ? 'bg-blue-600 text-white'
                : 'text-slate-300 hover:text-white hover:bg-slate-800'
            ]"
          >
            🌐 WebSocket 客户端
          </router-link>

          <router-link
            to="/httpRpc"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-colors',
              route.path === '/httpRpc'
                ? 'bg-purple-600 text-white'
                : 'text-slate-300 hover:text-white hover:bg-slate-800'
            ]"
          >
            🔗 HTTP RPC 客户端
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 页面内容 -->
    <RouterView />
  </div>
</template>

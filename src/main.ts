import ArcoVue from '@arco-design/web-vue'
import messages from '@intlify/unplugin-vue-i18n/messages'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import { createI18n } from 'vue-i18n'
import { createRouter, createWebHistory } from 'vue-router'
import { routes } from 'vue-router/auto-routes'
import App from './App.vue'

import '@arco-design/web-vue/dist/arco.css'
import '@unocss/reset/tailwind.css'
import 'uno.css'
import './style.css'
import './styles/main.css'

const router = createRouter({
  history: createWebHistory(),
  routes,
})

const i18n = createI18n({
  locale: 'zh',
  messages,
})
const pinia = createPinia()

const app = createApp(App)

app.use(pinia)
app.use(i18n)
app.use(ArcoVue)
app.use(router)
app.mount('#app')

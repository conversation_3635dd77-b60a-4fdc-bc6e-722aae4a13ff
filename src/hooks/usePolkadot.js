import { reactive, computed } from 'vue'
import { ApiPromise, WsProvider } from '@polkadot/api'
import { Keyring } from '@polkadot/keyring'
import { cryptoWaitReady } from '@polkadot/util-crypto'

// 测试助记词
const TEST_MNEMONIC = 'scan slow alter service unhappy capital acoustic picnic grant affair leader curtain'
// const TEST_MNEMONIC = 'bottom drive obey lake curtain smoke basket hold race lonely fit walk'

// 网络端点配置
const ENDPOINTS = {
  westend: 'wss://westend-rpc.polkadot.io',
  'westend-asset-hub': 'wss://westend-asset-hub-rpc.polkadot.io',
  rococo: 'wss://rococo-rpc.polkadot.io',
  'rococo-asset-hub': 'wss://rococo-asset-hub-rpc.polkadot.io',
  kusama: 'wss://kusama-rpc.polkadot.io',
  'kusama-asset-hub': 'wss://kusama-asset-hub-rpc.polkadot.io',
  polkadot: 'wss://rpc.polkadot.io',
  'polkadot-asset-hub': 'wss://polkadot-asset-hub-rpc.polkadot.io',
}

// 全局状态
const polkadotState = reactive({
  api: null,
  isConnected: false,
  isConnecting: false,
  currentEndpoint: ENDPOINTS['westend-asset-hub'], // 默认连接 Asset Hub
  keyring: null,
  accounts: [],
  selectedAccount: null,
  chainInfo: null,
  balances: {},
  transactions: [],
  error: null,
})

/**
 * Polkadot 相关功能的 composable hook
 */
export function usePolkadot() {
  // 计算属性
  const isReady = computed(() => polkadotState.api && polkadotState.isConnected && polkadotState.keyring)
  const currentBalance = computed(() => {
    if (!polkadotState.selectedAccount) return '0'
    return polkadotState.balances[polkadotState.selectedAccount.address] || '0'
  })

  // 连接到 Polkadot 网络
  const connect = async (endpoint) => {
    try {
      polkadotState.isConnecting = true
      polkadotState.error = null
      polkadotState.isConnected = false // 重置连接状态

      if (endpoint) {
        polkadotState.currentEndpoint = endpoint
      }

      console.log('🔄 开始连接到:', polkadotState.currentEndpoint)

      // 1. 等待加密库准备就绪
      console.log('⏳ 等待加密库准备就绪...')
      await cryptoWaitReady()
      console.log('✅ 加密库准备完成')

      // 2. 创建 WebSocket 提供者
      console.log('🌐 创建 WebSocket 连接...')
      const provider = new WsProvider(polkadotState.currentEndpoint, 10000) // 增加超时时间

      // 3. 创建 API 实例，使用最保守的配置
      console.log('🔧 创建 API 实例...')
      polkadotState.api = await ApiPromise.create({
        provider,
        throwOnConnect: false,
        throwOnUnknown: false,
        noInitWarn: true,
        // 添加兼容性配置
        types: {},
        rpc: {},
        signedExtensions: {},
        extrinsicType: 4,
      })

      console.log('✅ API 实例创建成功')

      // 4. 等待 API 完全准备就绪
      await polkadotState.api.isReady
      console.log('✅ API 完全准备就绪')

      // 5. 检查连接状态
      if (!polkadotState.api.isConnected) {
        throw new Error('API 创建成功但连接状态异常')
      }

      // 6. 设置连接状态监听
      provider.on('connected', () => {
        console.log('✅ WebSocket 连接已建立')
        if (!polkadotState.isConnected) {
          polkadotState.isConnected = true
        }
      })

      provider.on('disconnected', () => {
        console.log('❌ WebSocket 连接已断开')
        polkadotState.isConnected = false
      })

      provider.on('error', (error) => {
        console.error('WebSocket 错误:', error)
        polkadotState.error = `WebSocket 错误: ${error.message}`
        polkadotState.isConnected = false
      })

      // 7. 获取基本链信息
      console.log('📋 获取基本链信息...')
      await getChainInfoSafe()

      // 8. 初始化密钥环
      console.log('🔑 初始化密钥环...')
      await initKeyring()

      // 9. 最终设置连接状态
      polkadotState.isConnected = true
      console.log('🎉 Polkadot 连接完全建立')

    } catch (error) {
      polkadotState.error = `连接失败: ${error instanceof Error ? error.message : String(error)}`
      console.error('❌ 连接过程中发生错误:', error)
      console.error('❌ 错误堆栈:', error.stack)

      // 清理资源
      if (polkadotState.api) {
        try {
          await polkadotState.api.disconnect()
        } catch (e) {
          console.warn('断开连接时出错:', e)
        }
        polkadotState.api = null
      }
      polkadotState.isConnected = false
    } finally {
      polkadotState.isConnecting = false
    }
  }

  // 安全的链信息获取
  const getChainInfoSafe = async () => {
    if (!polkadotState.api) return

    try {
      // 只获取最基本的信息
      const chain = await polkadotState.api.rpc.system.chain()
      const version = await polkadotState.api.rpc.system.version()

      // 根据链名称和端点设置默认值，完全避免属性解析
      const chainName = chain.toString().toLowerCase()
      const endpoint = polkadotState.currentEndpoint.toLowerCase()
      let tokenSymbol = 'DOT'
      let tokenDecimals = 12

      if (chainName.includes('westend') || endpoint.includes('westend')) {
        tokenSymbol = 'WND'
        tokenDecimals = 12
      } else if (chainName.includes('rococo') || endpoint.includes('rococo')) {
        tokenSymbol = 'ROC'
        tokenDecimals = 12
      } else if (chainName.includes('kusama') || endpoint.includes('kusama')) {
        tokenSymbol = 'KSM'
        tokenDecimals = 12
      } else if (chainName.includes('polkadot') || endpoint.includes('polkadot')) {
        tokenSymbol = 'DOT'
        tokenDecimals = 10
      }

      polkadotState.chainInfo = {
        name: chain.toString(),
        version: version.toString(),
        tokenSymbol,
        tokenDecimals,
      }

      console.log('✅ 链信息获取成功:', polkadotState.chainInfo)
    } catch (error) {
      console.error('获取链信息失败:', error)
      // 设置默认值
      polkadotState.chainInfo = {
        name: '未知链',
        version: '未知版本',
        tokenSymbol: 'WND',
        tokenDecimals: 12,
      }
    }
  }

  // 初始化密钥环
  const initKeyring = async () => {
    try {
      polkadotState.keyring = new Keyring({ type: 'sr25519' })

      // 添加测试账户
      const testAccount = polkadotState.keyring.addFromMnemonic(TEST_MNEMONIC)
      polkadotState.accounts = [testAccount]
      polkadotState.selectedAccount = testAccount

      console.log('🔑 密钥环初始化完成')
      console.log('测试账户地址:', testAccount.address)

      // 获取账户余额
      await updateBalance(testAccount.address)

    } catch (error) {
      polkadotState.error = `密钥环初始化失败: ${error instanceof Error ? error.message : String(error)}`
      console.error('密钥环初始化失败:', error)
    }
  }

  // 格式化余额显示
  const formatBalanceDisplay = (
    balance,
    decimals = 12,
    symbol = 'DOT'
  ) => {
    if (!balance || balance === '0') return `0 ${symbol}`

    try {
      const bigBalance = BigInt(balance)
      const divisor = BigInt(10 ** decimals)
      const quotient = bigBalance / divisor
      const remainder = bigBalance % divisor

      // 格式化小数部分
      const decimalPart = remainder.toString().padStart(decimals, '0')
      let formattedDecimal = decimalPart.replace(/0+$/, '') // 移除尾随零
      if (formattedDecimal.length > 4) {
        formattedDecimal = formattedDecimal.slice(0, 4) // 最多显示4位小数
      }

      const result = formattedDecimal ? `${quotient}.${formattedDecimal}` : quotient.toString()
      return `${result} ${symbol}`
    } catch (error) {
      console.error('格式化余额失败:', error)
      return `${balance} ${symbol}`
    }
  }

  // 格式化地址显示 (显示前6位和后4位)
  const formatAddress = (address, prefixLength = 6, suffixLength = 4) => {
    if (!address || address.length <= prefixLength + suffixLength) {
      return address
    }
    return `${address.slice(0, prefixLength)}...${address.slice(-suffixLength)}`
  }

  // 复制到剪贴板
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch (error) {
      console.error('复制失败:', error)
      return false
    }
  }

  // 验证地址格式
  const isValidAddress = (address) => {
    // 基本的 Polkadot 地址验证 (SS58 格式)
    const ss58Regex = /^[1-9A-HJ-NP-Za-km-z]{47,48}$/
    return ss58Regex.test(address)
  }

  // 更新账户余额
  const updateBalance = async (address) => {
    if (!polkadotState.api) {
      console.warn('API 未准备就绪，无法获取余额')
      return
    }

    try {
      console.log(`🔍 查询账户余额: ${address}`)
      console.log('🔗 当前连接的链:', polkadotState.chainInfo?.name)
      console.log('🌐 当前端点:', polkadotState.currentEndpoint)

      // 使用更安全的方式查询余额
      const accountInfo = await polkadotState.api.query.system.account(address)

      console.log('📊 原始账户信息:', accountInfo)
      console.log('📊 账户信息类型:', typeof accountInfo)
      console.log('📊 账户信息结构:', Object.keys(accountInfo || {}))

      // 检查返回的数据结构
      if (accountInfo && accountInfo.data) {
        const balance = accountInfo.data
        console.log('💰 余额数据:', balance)
        console.log('💰 余额数据类型:', typeof balance)
        console.log('💰 余额数据结构:', Object.keys(balance || {}))

        const free = balance.free ? balance.free.toString() : '0'
        const reserved = balance.reserved ? balance.reserved.toString() : '0'
        const frozen = balance.frozen ? balance.frozen.toString() : '0'

        polkadotState.balances[address] = free
        console.log(`💰 账户 ${address.slice(0, 8)}... 详细余额:`)
        console.log(`  - 可用余额: ${free}`)
        console.log(`  - 保留余额: ${reserved}`)
        console.log(`  - 冻结余额: ${frozen}`)

        // 格式化显示
        const decimals = polkadotState.chainInfo?.tokenDecimals || 12
        const symbol = polkadotState.chainInfo?.tokenSymbol || 'WND'
        const formattedFree = formatBalanceDisplay(free, decimals, symbol)
        console.log(`  - 格式化可用余额: ${formattedFree}`)

      } else if (accountInfo) {
        // 尝试直接访问属性
        console.log('⚠️ 账户信息格式可能不同，尝试其他方式解析...')

        // 尝试不同的属性访问方式
        const possiblePaths = [
          () => accountInfo.free?.toString(),
          () => accountInfo.balance?.toString(),
          () => accountInfo.toString(),
          () => accountInfo.toHuman?.(),
          () => accountInfo.toJSON?.(),
        ]

        for (let i = 0; i < possiblePaths.length; i++) {
          try {
            const result = possiblePaths[i]()
            if (result) {
              console.log(`✅ 方式 ${i + 1} 成功:`, result)
            }
          } catch (e) {
            console.log(`❌ 方式 ${i + 1} 失败:`, e.message)
          }
        }

        polkadotState.balances[address] = '0'
      } else {
        console.warn('❌ 账户信息为空或未定义')
        polkadotState.balances[address] = '0'
      }
    } catch (error) {
      console.error('❌ 获取余额失败:', error)
      console.error('❌ 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      polkadotState.balances[address] = '0'

      // 如果是类型转换错误，提示用户
      if (error.message && error.message.includes('asVariant')) {
        console.warn('检测到类型转换错误，可能需要重新连接')
        polkadotState.error = '数据类型不兼容，请尝试重新连接'
      }
    }
  }

  // 断开连接
  const disconnect = async () => {
    if (polkadotState.api) {
      await polkadotState.api.disconnect()
      polkadotState.api = null
    }
    polkadotState.isConnected = false
    polkadotState.chainInfo = null
    polkadotState.accounts = []
    polkadotState.selectedAccount = null
    polkadotState.balances = {}
    console.log('🔌 已断开 Polkadot 连接')
  }

  // 更新所有账户余额
  const updateAllBalances = async () => {
    for (const account of polkadotState.accounts) {
      await updateBalance(account.address)
    }
  }

  // 获取最新区块信息
  const getLatestBlock = async () => {
    if (!polkadotState.api) return null

    try {
      const hash = await polkadotState.api.rpc.chain.getBlockHash()
      const block = await polkadotState.api.rpc.chain.getBlock(hash)
      const header = await polkadotState.api.rpc.chain.getHeader(hash)

      return {
        number: header.number.toNumber(),
        hash: hash.toString(),
        parentHash: header.parentHash.toString(),
        extrinsicsCount: block.block.extrinsics.length,
        timestamp: Date.now(),
      }
    } catch (error) {
      console.error('获取区块信息失败:', error)
      return null
    }
  }

  // 发送转账
  const transfer = async (to, amount) => {
    console.log('%c [ to, amount ]', 'font-size:13px; background:pink; color:#bf2c9f;', to, amount)
    if (!polkadotState.api || !polkadotState.selectedAccount) {
      throw new Error('API 或账户未准备就绪')
    }

    if (!polkadotState.isConnected) {
      throw new Error('网络连接未建立')
    }

    try {
      console.log('🚀 开始转账:', { to, amount })
      console.log('📝 发送方:', polkadotState.selectedAccount.address)

      // 验证地址格式
      if (!isValidAddress(to)) {
        throw new Error('接收地址格式无效')
      }

      // 检查余额是否足够
      if (!hasEnoughBalance(amount)) {
        throw new Error('余额不足')
      }

      // 转换金额为链上单位
      const chainAmount = toChainUnits(amount, polkadotState.chainInfo?.tokenDecimals || 12)
      console.log('💰 转账金额 (链上单位):', chainAmount)

      // 创建转账交易
      const transferTx = polkadotState.api.tx.balances.transferKeepAlive(to, chainAmount)
      console.log('📋 交易已创建')

      // 获取交易费用估算
      try {
        const paymentInfo = await transferTx.paymentInfo(polkadotState.selectedAccount)
        console.log('💸 预估手续费:', paymentInfo.partialFee.toHuman())
      } catch (feeError) {
        console.warn('无法获取手续费估算:', feeError.message)
      }

      // 使用更兼容的签名和发送方式
      const result = await new Promise((resolve, reject) => {
        let unsubscribe = null

        const handleResult = ({ status, events = [], dispatchError }) => {
          console.log('📊 交易状态:', status.type)

          // 检查是否有调度错误
          if (dispatchError) {
            let errorMessage = '交易执行失败'

            if (dispatchError.isModule) {
              // 模块错误
              try {
                const decoded = polkadotState.api.registry.findMetaError(dispatchError.asModule)
                errorMessage = `${decoded.section}.${decoded.name}: ${decoded.docs.join(' ')}`
              } catch (e) {
                errorMessage = dispatchError.toString()
              }
            } else {
              // 其他错误
              errorMessage = dispatchError.toString()
            }

            console.error('❌ 调度错误:', errorMessage)
            if (unsubscribe) unsubscribe()
            reject(new Error(errorMessage))
            return
          }

          if (status.isInBlock) {
            console.log('✅ 交易已打包到区块:', status.asInBlock.toString())

            // 检查事件中是否有错误
            const failedEvent = events.find(({ event }) =>
              polkadotState.api.events.system.ExtrinsicFailed.is(event)
            )

            if (failedEvent) {
              console.error('❌ 交易执行失败:', failedEvent.event.data.toString())
              if (unsubscribe) unsubscribe()
              reject(new Error('交易执行失败'))
              return
            }

            // 查找转账成功事件
            const transferEvent = events.find(({ event }) =>
              polkadotState.api.events.balances.Transfer.is(event)
            )

            if (transferEvent) {
              console.log('💸 转账事件:', transferEvent.event.data.toString())
            }

          } else if (status.isFinalized) {
            console.log('🎉 交易已确认:', status.asFinalized.toString())

            // 更新余额
            updateBalance(polkadotState.selectedAccount.address)
            updateBalance(to)

            if (unsubscribe) unsubscribe()
            resolve(status.asFinalized.toString())
          } else if (status.isDropped || status.isInvalid || status.isUsurped) {
            console.error('❌ 交易失败:', status.type)
            if (unsubscribe) unsubscribe()
            reject(new Error(`交易失败: ${status.type}`))
          }
        }

        // 尝试使用更兼容的签名方式
        try {
          // 方法1: 直接使用 signAndSend
          transferTx.signAndSend(polkadotState.selectedAccount, { nonce: -1 }, handleResult)
            .then((unsub) => {
              unsubscribe = unsub
              console.log('📤 交易已提交到网络')
            })
            .catch((signError) => {
              console.error('❌ 签名失败，尝试备用方法:', signError)

              // 方法2: 分步签名和发送
              transferTx.sign(polkadotState.selectedAccount, { nonce: -1 })
                .then(() => {
                  console.log('✅ 交易签名成功')
                  return transferTx.send(handleResult)
                })
                .then((unsub) => {
                  unsubscribe = unsub
                  console.log('📤 交易已提交到网络 (备用方法)')
                })
                .catch((sendError) => {
                  console.error('❌ 发送失败:', sendError)
                  reject(sendError)
                })
            })
        } catch (error) {
          console.error('❌ 签名或发送失败:', error)
          reject(error)
        }
      })

      // 记录交易
      polkadotState.transactions.unshift({
        hash: result,
        from: polkadotState.selectedAccount.address,
        to,
        amount: chainAmount,
        timestamp: Date.now(),
        status: 'finalized',
      })

      console.log('🎉 转账成功完成')
      return result

    } catch (error) {
      console.error('❌ 转账失败:', error)
      console.error('❌ 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      throw error
    }
  }

  // 添加新账户
  const addAccount = (mnemonic, name) => {
    if (!polkadotState.keyring) {
      throw new Error('密钥环未初始化')
    }

    try {
      const account = polkadotState.keyring.addFromMnemonic(mnemonic, { name })
      polkadotState.accounts.push(account)
      updateBalance(account.address)
      return account
    } catch (error) {
      console.error('添加账户失败:', error)
      throw error
    }
  }

  // 选择账户
  const selectAccount = (address) => {
    const account = polkadotState.accounts.find(acc => acc.address === address)
    if (account) {
      polkadotState.selectedAccount = account
      console.log('🔄 切换到账户:', address)
    }
  }

  // 清除错误
  const clearError = () => {
    polkadotState.error = null
  }

  // 转换单位 (从用户输入的数值转换为最小单位)
  const toChainUnits = (amount, decimals = 12) => {
    const numAmount = typeof amount === 'string' ? Number.parseFloat(amount) : amount
    if (Number.isNaN(numAmount)) return '0'

    return BigInt(Math.floor(numAmount * (10 ** decimals))).toString()
  }

  // 从最小单位转换为用户友好的数值
  const fromChainUnits = (amount, decimals = 12) => {
    try {
      const bigAmount = BigInt(amount)
      const divisor = BigInt(10 ** decimals)
      const quotient = bigAmount / divisor
      const remainder = bigAmount % divisor

      const decimalPart = remainder.toString().padStart(decimals, '0')
      return `${quotient}.${decimalPart}`.replace(/\.?0+$/, '')
    } catch (error) {
      console.error('转换单位失败:', error)
      return '0'
    }
  }

  // 获取网络状态信息
  const getNetworkStatus = () => {
    return {
      isConnected: polkadotState.isConnected,
      isConnecting: polkadotState.isConnecting,
      isReady: isReady.value,
      chainName: polkadotState.chainInfo?.name || '',
      tokenSymbol: polkadotState.chainInfo?.tokenSymbol || '',
      tokenDecimals: polkadotState.chainInfo?.tokenDecimals || 12,
      currentEndpoint: polkadotState.currentEndpoint,
      error: polkadotState.error,
    }
  }

  // 获取当前账户信息
  const getCurrentAccount = () => {
    if (!polkadotState.selectedAccount) return null

    return {
      address: polkadotState.selectedAccount.address,
      name: polkadotState.selectedAccount.meta.name || '未命名账户',
      balance: currentBalance.value,
      formattedBalance: formatBalanceDisplay(
        currentBalance.value,
        polkadotState.chainInfo?.tokenDecimals,
        polkadotState.chainInfo?.tokenSymbol
      ),
      formattedAddress: formatAddress(polkadotState.selectedAccount.address),
    }
  }

  // 获取所有账户信息
  const getAllAccounts = () => {
    return polkadotState.accounts.map(account => ({
      address: account.address,
      name: account.meta.name || '未命名账户',
      balance: polkadotState.balances[account.address] || '0',
      formattedBalance: formatBalanceDisplay(
        polkadotState.balances[account.address] || '0',
        polkadotState.chainInfo?.tokenDecimals,
        polkadotState.chainInfo?.tokenSymbol
      ),
      formattedAddress: formatAddress(account.address),
      isSelected: polkadotState.selectedAccount?.address === account.address,
    }))
  }

  const connectToTestnet = async () => {
    try {
      await connect('wss://westend-asset-hub-rpc.polkadot.io')
      return true
    } catch (error) {
      console.error('连接测试网失败:', error)
      return false
    }
  }

  // 检查余额是否足够进行转账
  const hasEnoughBalance = (amount, includeExistentialDeposit = true) => {
    if (!polkadotState.selectedAccount) {
      console.warn('❌ 余额检查失败: 没有选中的账户')
      return false
    }

    const currentBalanceValue = BigInt(currentBalance.value || '0')
    const decimals = polkadotState.chainInfo?.tokenDecimals || 12
    const transferAmount = BigInt(toChainUnits(amount, decimals))

    // 预留一些余额作为手续费和存在性保证金
    const reserveAmount = BigInt(toChainUnits('0.1', decimals))
    const totalRequired = transferAmount + (includeExistentialDeposit ? reserveAmount : BigInt(0))

    console.log('💰 余额检查详情:')
    console.log(`  - 当前余额: ${currentBalanceValue.toString()} (${fromChainUnits(currentBalanceValue.toString(), decimals)} ${polkadotState.chainInfo?.tokenSymbol || 'WND'})`)
    console.log(`  - 转账金额: ${transferAmount.toString()} (${amount} ${polkadotState.chainInfo?.tokenSymbol || 'WND'})`)
    console.log(`  - 预留金额: ${reserveAmount.toString()} (0.1 ${polkadotState.chainInfo?.tokenSymbol || 'WND'})`)
    console.log(`  - 总需求: ${totalRequired.toString()}`)
    console.log(`  - 是否足够: ${currentBalanceValue >= totalRequired}`)

    return currentBalanceValue >= totalRequired
  }

  return {
    // 状态
    store: polkadotState,
    isReady,
    currentBalance,

    // 工具函数
    formatBalanceDisplay,
    formatAddress,
    copyToClipboard,
    isValidAddress,
    toChainUnits,
    fromChainUnits,

    // 状态获取
    getNetworkStatus,
    getCurrentAccount,
    getAllAccounts,

    // 快捷操作
    connectToTestnet,
    hasEnoughBalance,

    // 核心方法
    connect,
    disconnect,
    updateBalance,
    updateAllBalances,
    transfer,
    addAccount,
    selectAccount,
    getLatestBlock,
    clearError,
  }
}

// 导出一些常用的常量
export const POLKADOT_ENDPOINTS = {
  westend: 'wss://westend-rpc.polkadot.io',
  rococo: 'wss://rococo-rpc.polkadot.io',
  kusama: 'wss://kusama-rpc.polkadot.io',
  polkadot: 'wss://rpc.polkadot.io',
}

export const TEST_ACCOUNTS = {
  alice: 'bottom drive obey lake curtain smoke basket hold race lonely fit walk',
  bob: 'bottom drive obey lake curtain smoke basket hold race lonely fit walk//Bob',
  charlie: 'bottom drive obey lake curtain smoke basket hold race lonely fit walk//Charlie',
}

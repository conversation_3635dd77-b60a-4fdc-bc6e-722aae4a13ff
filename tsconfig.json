{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "paths": {
      "~/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "vitest",
      "vite/client",
      "unplugin-vue-router/client"
    ],
    "allowJs": true,
    "strict": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "noEmit": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "vueCompilerOptions": {
    "plugins": [
      // "@vue-macros/volar/define-models",
      // "@vue-macros/volar/define-slots"
    ]
  },
  "include": ["src/**/*.d.ts"],

  "exclude": ["dist", "node_modules", "cypress"]
}

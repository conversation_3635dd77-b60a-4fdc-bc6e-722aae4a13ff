# Polkadot HTTP RPC 客户端

这是一个基于 Polkadot.js API 的 HTTP RPC 客户端实现，展示了如何使用 HTTP Provider 与 Polkadot 节点进行交互。

## 🌟 功能特性

### ✅ 已实现功能
- **HTTP RPC 连接**: 使用 `HttpProvider` 连接到本地或远程 Polkadot 节点
- **账户管理**: 基于助记词的账户创建和管理
- **余额查询**: 实时查询账户余额
- **转账功能**: 发送代币转账交易
- **链信息获取**: 获取链名称、版本、代币信息等
- **区块信息**: 查询最新区块信息
- **地址验证**: SS58 格式地址验证
- **余额检查**: 转账前余额充足性检查
- **实时日志**: 详细的操作日志记录

### ⚠️ HTTP 模式限制
- **无法跟踪交易状态**: HTTP 是无状态协议，无法订阅交易状态变化
- **无实时事件**: 不支持链上事件订阅
- **只能获取交易哈希**: 转账后只能获得交易哈希，需要手动查询确认状态

## 🚀 快速开始

### 1. 启动本地 Polkadot 节点
确保你有一个运行在 `http://127.0.0.1:9933` 的 Polkadot 节点，并启用了 HTTP RPC：

```bash
# 启动带有 HTTP RPC 的本地节点
polkadot --dev --rpc-cors all --rpc-methods unsafe --rpc-external
```

### 2. 访问 HTTP RPC 客户端
在浏览器中访问：
```
http://localhost:3333/httpRpc
```

### 3. 连接和使用
1. 点击 "🔌 连接到 HTTP RPC" 按钮
2. 等待连接建立和账户初始化
3. 查看账户余额和链信息
4. 进行转账操作

## 🔧 技术实现

### 核心依赖
- `@polkadot/api`: Polkadot.js API 核心库
- `@polkadot/keyring`: 密钥管理
- `@polkadot/util-crypto`: 加密工具

### 关键代码结构

#### HTTP Provider 初始化
```javascript
import { ApiPromise, HttpProvider } from '@polkadot/api'

// 创建 HTTP Provider
const provider = new HttpProvider('http://127.0.0.1:9933')

// 创建 API 实例
const api = await ApiPromise.create({ provider })
```

#### 余额查询
```javascript
const accountInfo = await api.query.system.account(address)
const balance = accountInfo.data.free.toString()
```

#### 转账操作
```javascript
const transfer = api.tx.balances.transferKeepAlive(recipient, amount)
const hash = await transfer.signAndSend(senderAccount)
// 注意：HTTP 模式下只能获取交易哈希，无法跟踪状态
```

## 🎨 UI 设计

### 设计风格
- **Notion 风格**: 简洁、现代的界面设计
- **UnoCSS**: 原子化 CSS 框架
- **响应式布局**: 适配不同屏幕尺寸
- **实时反馈**: 即时的状态更新和错误提示

### 颜色方案
- 主色调：深色背景 + 紫色渐变
- 状态指示：绿色(成功)、红色(错误)、黄色(警告)、蓝色(信息)
- 交互元素：悬停效果和过渡动画

## 📝 使用说明

### 连接配置
- **默认端点**: `http://127.0.0.1:9933`
- **测试账户**: 使用预设助记词生成的测试账户
- **支持的链**: 任何兼容 Substrate 的区块链

### 转账操作
1. **地址验证**: 自动验证 SS58 格式地址
2. **余额检查**: 确保有足够余额支付转账和手续费
3. **实时反馈**: 显示转账状态和交易哈希
4. **错误处理**: 详细的错误信息和建议

### 安全注意事项
- 仅用于开发和测试环境
- 不要在生产环境中使用硬编码的助记词
- 确保 RPC 端点的安全性

## 🔄 与 WebSocket 模式对比

| 功能 | HTTP RPC | WebSocket |
|------|----------|-----------|
| 连接方式 | 无状态请求 | 持久连接 |
| 查询操作 | ✅ 支持 | ✅ 支持 |
| 转账操作 | ✅ 支持 | ✅ 支持 |
| 交易状态跟踪 | ❌ 不支持 | ✅ 支持 |
| 事件订阅 | ❌ 不支持 | ✅ 支持 |
| 实时更新 | ❌ 需要轮询 | ✅ 自动推送 |
| 资源消耗 | 低 | 中等 |
| 适用场景 | 简单查询 | 完整 dApp |

## 🛠️ 开发指南

### 添加新功能
1. 在 `httpRpcState` 中添加状态
2. 创建对应的处理函数
3. 在模板中添加 UI 组件
4. 更新日志记录

### 错误处理
- 使用 `addLog()` 函数记录操作日志
- 区分不同类型的日志：info、success、warning、error
- 提供用户友好的错误信息

### 样式定制
- 基于 UnoCSS 的原子化 CSS
- 使用 Tailwind CSS 兼容的类名
- 支持深色主题

## 📚 相关资源

- [Polkadot.js API 文档](https://polkadot.js.org/docs/)
- [Substrate 开发文档](https://docs.substrate.io/)
- [UnoCSS 文档](https://unocss.dev/)
- [Vue 3 文档](https://vuejs.org/)

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目！

### 开发环境
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

## 📄 许可证

MIT License

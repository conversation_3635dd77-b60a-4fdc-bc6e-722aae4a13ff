{"name": "vue-template-mini", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --port 3333 --mode development", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@polkadot/api": "^14.2.2", "@polkadot/keyring": "^13.2.1", "@polkadot/types": "^14.2.2", "@polkadot/util": "^13.2.1", "@polkadot/util-crypto": "^13.2.1", "@unocss/reset": "^65.4.3", "@vueuse/core": "^12.4.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "radash": "^12.1.0", "vue": "^3.5.13", "vue-i18n": "^11.0.1", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^4.2.0", "@arco-design/web-vue": "^2.56.3", "@iconify-json/carbon": "^1.2.8", "@intlify/unplugin-vue-i18n": "^6.0.3", "@types/node": "^22.13.5", "@unocss/eslint-plugin": "^65.4.3", "@vitejs/plugin-vue": "^5.2.1", "code-inspector-plugin": "^0.20.0", "eslint": "^9.18.0", "eslint-plugin-format": "^1.0.1", "typescript": "^5.7.3", "unocss": "^65.4.3", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "unplugin-vue-router": "^0.11.2", "vite": "npm:rolldown-vite@^6.3.17", "vite-plugin-vue-devtools": "^7.7.0", "vitest": "^3.0.2", "vue-tsc": "^2.2.0"}}
import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  presetUno,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  shortcuts: [
    ['text-desc', 'text-sm text-white/60'],
    ['btn', 'rounded-full px-10 py-1.5 !b-0 flex-center gap-2'],
    ['icon-btn', 'inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-teal-600'],
    // 自定义配置
    ['inset-center', 'absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2'],
    ['inset-x-center', 'absolute left-1/2 transform -translate-x-1/2'],
    ['inset-y-center', 'absolute top-1/2 transform -translate-y-1/2'],
    ['flex-col', 'flex flex-col'],
    ['ellipsis', 'truncate'],
    ['flex-center', 'flex items-center'],
    ['flex-middle', 'flex justify-center items-center'],
    ['flex-between', 'flex justify-between items-center'],
    ['flex-around', 'flex justify-around items-center'],
    ['flex-column', 'flex flex-col'],
    ['flex-end', 'flex justify-end'],
    ['flex-start', 'flex justify-start'],
    ['b1', 'border-1px border-solid'],
  ],
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({ scale: 1.2 }),
    presetTypography(),
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  safelist: 'prose prose-sm m-auto text-left'.split(' '),
})
